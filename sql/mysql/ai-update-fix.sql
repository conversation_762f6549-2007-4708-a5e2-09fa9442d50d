-- AI模块数据库表结构修复脚本
-- 用于修复现有数据库中缺失的字段

-- 修复 ai_chat_conversation 表
-- 添加 pinned_time 字段
ALTER TABLE `ai_chat_conversation` 
ADD COLUMN `pinned_time` datetime DEFAULT NULL COMMENT '置顶时间' AFTER `pinned`;

-- 添加 system_message 字段（如果不存在）
ALTER TABLE `ai_chat_conversation` 
ADD COLUMN `system_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '角色设定' AFTER `model`;

-- 检查并创建其他可能缺失的AI相关表

-- 创建 ai_api_key 表（如果不存在）
CREATE TABLE IF NOT EXISTS `ai_api_key` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称',
  `platform` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '平台',
  `api_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密钥',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '自定义 API 地址',
  `status` int NOT NULL COMMENT '状态',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI API 密钥表';

-- 创建 ai_model 表（如果不存在）
CREATE TABLE IF NOT EXISTS `ai_model` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模型名字',
  `model` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模型标识',
  `platform` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模型平台',
  `type` int NOT NULL COMMENT '模型类型',
  `key_id` bigint NOT NULL COMMENT 'API 秘钥编号',
  `sort` int NOT NULL COMMENT '排序',
  `status` tinyint NOT NULL COMMENT '状态',
  `temperature` double DEFAULT NULL COMMENT '温度参数',
  `max_tokens` int DEFAULT NULL COMMENT '单条回复的最大 Token 数量',
  `max_contexts` int DEFAULT NULL COMMENT '上下文的最大 Message 数量',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI 模型表';

-- 创建 ai_chat_message 表（如果不存在）
CREATE TABLE IF NOT EXISTS `ai_chat_message` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `conversation_id` bigint NOT NULL COMMENT '对话编号',
  `reply_id` bigint DEFAULT NULL COMMENT '回复消息编号',
  `type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息类型',
  `user_id` bigint NOT NULL COMMENT '用户编号',
  `role_id` bigint DEFAULT NULL COMMENT '角色编号',
  `model_id` bigint DEFAULT NULL COMMENT '模型编号',
  `model` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模型标识',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '聊天内容',
  `use_context` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否使用上下文',
  `segment_ids` json DEFAULT NULL COMMENT '知识库段落编号数组',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI 聊天消息表';

-- 创建 ai_chat_role 表（如果不存在）
CREATE TABLE IF NOT EXISTS `ai_chat_role` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色名称',
  `avatar` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '角色头像',
  `category` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '角色分类',
  `description` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '角色描述',
  `system_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '角色设定',
  `user_id` bigint DEFAULT NULL COMMENT '用户编号',
  `model_id` bigint NOT NULL COMMENT '模型编号',
  `knowledge_ids` json DEFAULT NULL COMMENT '引用的知识库编号列表',
  `tool_ids` json DEFAULT NULL COMMENT '引用的工具编号列表',
  `public_status` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否公开',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序值',
  `status` int NOT NULL DEFAULT '0' COMMENT '状态',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI 聊天角色表';

-- 插入一些基础数据（如果表为空）
-- 插入默认AI模型配置（需要先有api_key数据）
INSERT IGNORE INTO `ai_api_key` (`id`, `name`, `platform`, `api_key`, `status`, `creator`, `updater`) VALUES 
(1, 'OpenAI默认', 'OpenAI', 'your-openai-api-key-here', 0, 'admin', 'admin');

INSERT IGNORE INTO `ai_model` (`id`, `name`, `model`, `platform`, `type`, `key_id`, `sort`, `status`, `temperature`, `max_tokens`, `max_contexts`, `creator`, `updater`) VALUES 
(1, 'GPT-3.5 Turbo', 'gpt-3.5-turbo', 'OpenAI', 1, 1, 1, 0, 0.7, 2048, 10, 'admin', 'admin'),
(2, 'GPT-4', 'gpt-4', 'OpenAI', 1, 1, 2, 0, 0.7, 4096, 20, 'admin', 'admin');

-- 插入默认聊天角色
INSERT IGNORE INTO `ai_chat_role` (`id`, `name`, `avatar`, `category`, `description`, `system_message`, `model_id`, `public_status`, `sort`, `status`, `creator`, `updater`) VALUES 
(1, '智能助手', 'https://www.iocoder.cn/avatar/ai-assistant.png', '通用', '我是一个智能助手，可以帮助您解答各种问题', '你是一个智能助手，请用友好、专业的语气回答用户的问题。', 1, 1, 1, 0, 'admin', 'admin');

-- 显示修复完成信息
SELECT 'AI模块数据库表结构修复完成！' as message;
