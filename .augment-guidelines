# 若依Vue Pro项目 - Augment AI助手配置指南
# ================================================================
# 项目: 若依Vue Pro (ruoyi-vue-pro)
# 版本: 2.6.1-SNAPSHOT
# 架构: 前后端分离 + 微服务模块化
# 技术栈: Spring Boot 3.5.3 + Vue 3.5.12 + TypeScript
# ================================================================

## 项目基本信息
project:
  name: "若依Vue Pro"
  description: "基于Spring Boot 3.5.3 + Vue 3的企业级快速开发平台"
  version: "2.6.1-SNAPSHOT"
  architecture: "前后端分离 + 微服务模块化"
  type: "企业级快速开发平台"

## 核心技术栈配置
tech_stack:
  backend:
    framework: "Spring Boot 3.5.3"
    orm: "MyBatis Plus 3.5.12"
    database: "MySQL 8.0 + Redis 7.0"
    security: "Spring Security + JWT"
    validation: "Spring Validation"
    cache: "Redis + Spring Cache"

  frontend:
    framework: "Vue 3.5.12"
    ui_library: "Element Plus 2.9.1"
    language: "TypeScript 5.6.3"
    build_tool: "Vite 6.0.1"
    state_management: "Pinia 2.1.7"
    router: "Vue Router 4.4.5"

  ai_integration:
    platforms: ["OpenAI", "智谱AI", "通义千问", "文心一言", "Midjourney", "Stable Diffusion"]
    features: ["聊天对话", "知识库RAG", "AI绘图", "AI音乐", "代码生成"]
    vector_store: "Redis Vector + Qdrant"

## 项目结构规范
project_structure:
  backend_modules:
    - "yudao-dependencies: 依赖管理模块"
    - "yudao-framework: 框架核心模块"
    - "yudao-server: 服务启动模块"
    - "yudao-module-system: 系统管理模块"
    - "yudao-module-infra: 基础设施模块"
    - "yudao-module-ai: AI功能模块"
    - "yudao-module-mall: 商城模块"
    - "yudao-module-report: 报表模块"

  frontend_structure:
    - "src/api: API接口定义"
    - "src/components: 公共组件"
    - "src/views: 页面视图"
    - "src/store: Pinia状态管理"
    - "src/router: 路由配置"
    - "src/utils: 工具函数"
    - "src/hooks: 组合式API"

## 开发规范与最佳实践
coding_standards:
  backend:
    architecture: "严格遵循Controller -> Service -> Mapper三层架构"
    naming_convention:
      - "Controller类以Controller结尾"
      - "Service类以Service结尾，接口以Service结尾，实现类以ServiceImpl结尾"
      - "Mapper类以Mapper结尾"
      - "实体类使用DO后缀，VO类使用VO后缀，DTO类使用DTO后缀"

    api_design:
      - "遵循RESTful API设计原则"
      - "使用统一的Result<T>返回格式"
      - "API路径使用/admin-api/前缀"
      - "使用@PreAuthorize注解进行权限控制"

    exception_handling:
      - "使用GlobalExceptionHandler统一异常处理"
      - "自定义业务异常继承ServiceException"
      - "错误码统一定义在ErrorCodeConstants中"

    database:
      - "实体类使用@TableName注解指定表名"
      - "主键使用@TableId(type = IdType.NONE)自适应策略"
      - "逻辑删除字段统一使用deleted"
      - "创建时间create_time，更新时间update_time"

  frontend:
    component_design:
      - "优先使用组合式API (Composition API)"
      - "组件名使用PascalCase命名"
      - "Props定义使用TypeScript接口"
      - "事件使用defineEmits定义"

    state_management:
      - "使用Pinia进行状态管理"
      - "Store按模块划分：user, app, permission等"
      - "持久化使用pinia-plugin-persistedstate"

    routing:
      - "路由配置支持动态加载"
      - "使用路由守卫进行权限控制"
      - "页面组件懒加载"

    styling:
      - "使用SCSS预处理器"
      - "遵循BEM命名规范"
      - "使用CSS变量进行主题配置"

## AI功能开发指南
ai_development:
  chat_module:
    - "聊天对话基于ai_chat_conversation和ai_chat_message表"
    - "支持多种AI模型切换"
    - "实现流式响应和上下文管理"

  knowledge_base:
    - "知识库基于RAG架构实现"
    - "文档解析使用Apache Tika"
    - "向量化存储支持Redis Vector和Qdrant"
    - "相似性搜索基于余弦相似度"

  image_generation:
    - "支持Midjourney、Stable Diffusion等平台"
    - "图片生成记录存储在ai_image表"
    - "支持多种尺寸和风格参数"

## 代码质量要求
quality_standards:
  code_review:
    - "所有代码必须通过ESLint和Prettier检查"
    - "后端代码遵循阿里巴巴Java开发手册"
    - "前端代码遵循Vue 3官方风格指南"

  testing:
    - "单元测试覆盖率不低于70%"
    - "关键业务逻辑必须有集成测试"
    - "API接口必须有完整的测试用例"

  documentation:
    - "所有API使用Swagger/OpenAPI文档"
    - "复杂业务逻辑必须有详细注释"
    - "README文件保持更新"

## 性能优化指南
performance:
  backend:
    - "数据库查询使用索引优化"
    - "大数据量查询使用分页"
    - "频繁查询数据使用Redis缓存"
    - "AI接口调用实现异步处理"

  frontend:
    - "组件懒加载和代码分割"
    - "图片资源使用CDN"
    - "长列表使用虚拟滚动"
    - "API请求去重和缓存"

## 安全规范
security:
  authentication:
    - "使用JWT Token进行身份认证"
    - "Token过期时间合理设置"
    - "敏感操作需要二次验证"

  authorization:
    - "基于RBAC权限模型"
    - "菜单权限和按钮权限分离"
    - "数据权限支持部门和个人级别"

  data_protection:
    - "敏感数据加密存储"
    - "API接口参数校验"
    - "SQL注入防护"
    - "XSS攻击防护"

## 部署与运维
deployment:
  environment:
    - "支持local/dev/test/prod多环境配置"
    - "使用Docker容器化部署"
    - "支持Kubernetes集群部署"

  monitoring:
    - "集成Spring Boot Actuator健康检查"
    - "使用Druid监控数据库连接"
    - "AI接口调用监控和限流"

  backup:
    - "数据库定期备份"
    - "重要配置文件版本控制"
    - "AI模型配置备份"

## AI助手工作指导
ai_assistant_guidelines:
  code_analysis:
    - "优先分析现有代码结构和设计模式"
    - "识别项目中的技术债务和改进点"
    - "关注AI功能模块的特殊性"

  code_generation:
    - "生成的代码必须符合项目现有规范"
    - "新增功能要考虑与现有模块的集成"
    - "AI相关代码要考虑异步处理和错误重试"

  refactoring:
    - "重构时保持向后兼容性"
    - "优先重构高复杂度和高耦合的代码"
    - "重构后必须保证测试通过"

  problem_solving:
    - "问题分析要考虑前后端交互"
    - "性能问题优先检查数据库和缓存"
    - "AI功能问题要检查模型配置和网络连接"

## 常用命令和工具
development_tools:
  backend:
    - "mvn clean install: 构建项目"
    - "mvn spring-boot:run: 启动应用"
    - "mvn test: 运行测试"

  frontend:
    - "pnpm install: 安装依赖"
    - "pnpm dev: 启动开发服务器"
    - "pnpm build: 构建生产版本"
    - "pnpm lint: 代码检查"

  database:
    - "sql/mysql目录包含初始化脚本"
    - "支持MySQL 8.0+版本"
    - "Redis用于缓存和会话存储"

## 特别注意事项
special_notes:
  ai_features:
    - "AI功能需要配置相应的API密钥"
    - "向量数据库需要单独部署和配置"
    - "AI接口调用有频率限制，需要合理控制"

  compatibility:
    - "JDK版本必须是17+"
    - "Node.js版本必须是16.18.0+"
    - "强制使用pnpm作为包管理器"

  licensing:
    - "项目采用MIT开源协议"
    - "所有代码完全开源，无商业版本"
    - "可用于个人和企业项目"