@echo off
title RuoYi Vue Pro - Simple Build

echo ========================================
echo    RuoYi Vue Pro Simple Build
echo ========================================
echo.

:: Configuration
set PROJECT_ROOT=G:\devAI\ruoyi-vue-pro-1735
set JAVA_HOME=E:\Java\jdk-17.0.16.8
set MAVEN_HOME=D:\maven\apache-maven-3.8.1
set MAVEN_SETTINGS=D:\maven\apache-maven-3.8.1\conf\settings_nis.xml
set MAVEN_REPO=D:\maven\nisRepository

:: Set environment - Force JAVA_HOME and PATH
set JAVA_HOME=%JAVA_HOME%
set PATH=%JAVA_HOME%\bin;%MAVEN_HOME%\bin;%PATH%
set MAVEN_OPTS=-Xms512m -Xmx1024m

echo [INFO] Active modules:
echo   - yudao-dependencies
echo   - yudao-framework  
echo   - yudao-server
echo   - yudao-module-system
echo   - yudao-module-infra
echo   - yudao-module-ai
echo   - yudao-module-report
echo.

echo [INFO] Environment check...
java -version 2>&1 | findstr "openjdk version"
echo.

cd /d "%PROJECT_ROOT%"

echo [STEP 1/3] Cleaning...
mvn clean -s "%MAVEN_SETTINGS%" -Dmaven.repo.local="%MAVEN_REPO%" -q
if errorlevel 1 (
    echo [ERROR] Clean failed
    pause
    exit /b 1
)
echo [SUCCESS] Clean completed

echo.
echo [STEP 2/3] Compiling...
mvn compile -s "%MAVEN_SETTINGS%" -Dmaven.repo.local="%MAVEN_REPO%" -T 1C
if errorlevel 1 (
    echo [ERROR] Compile failed
    pause
    exit /b 1
)
echo [SUCCESS] Compile completed

echo.
echo [STEP 3/3] Packaging...
mvn package -s "%MAVEN_SETTINGS%" -Dmaven.repo.local="%MAVEN_REPO%" -DskipTests -T 1C
if errorlevel 1 (
    echo [ERROR] Package failed
    pause
    exit /b 1
)
echo [SUCCESS] Package completed

echo.
echo ========================================
echo [BUILD SUCCESS] All modules built!
echo ========================================

:: Check JAR file
set JAR_FILE=%PROJECT_ROOT%\yudao-server\target\yudao-server.jar
if exist "%JAR_FILE%" (
    echo [INFO] JAR file created: %JAR_FILE%
    for %%I in ("%JAR_FILE%") do echo [INFO] Size: %%~zI bytes
) else (
    echo [WARNING] JAR file not found
)

echo.
echo [INFO] Ready to start with start-simple.bat
pause
