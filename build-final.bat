@echo off
title RuoYi Vue Pro - Final Build Script

echo ========================================
echo    RuoYi Vue Pro Final Build Script
echo ========================================
echo.

:: Configuration parameters
set PROJECT_ROOT=G:\devAI\ruoyi-vue-pro-1735
set JAVA_HOME=E:\Java\jdk-*********
set MAVEN_HOME=D:\maven\apache-maven-3.8.1
set MAVEN_SETTINGS=D:\maven\apache-maven-3.8.1\conf\settings_nis.xml
set MAVEN_REPO=D:\maven\nisRepository

:: Force set environment variables
set JAVA_HOME=%JAVA_HOME%
set PATH=%JAVA_HOME%\bin;%MAVEN_HOME%\bin;%PATH%
set MAVEN_OPTS=-Xms512m -Xmx2048m -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m

echo [INFO] Environment Configuration:
echo   JAVA_HOME: %JAVA_HOME%
echo   MAVEN_HOME: %MAVEN_HOME%
echo   PROJECT_ROOT: %PROJECT_ROOT%
echo.

echo [INFO] Checking Java and Maven...
java -version 2>&1 | findstr "openjdk version"
if errorlevel 1 (
    echo [ERROR] Java not found or not working
    pause
    exit /b 1
)

%MAVEN_HOME%\bin\mvn.cmd -version 2>&1 | findstr "Apache Maven"
if errorlevel 1 (
    echo [ERROR] Maven not found or not working
    pause
    exit /b 1
)

echo [SUCCESS] Java and Maven are working
echo.

echo [INFO] Active modules after mall module removal:
echo   - yudao-dependencies (dependency management)
echo   - yudao-framework (core framework)
echo   - yudao-server (main application)
echo   - yudao-module-system (system management)
echo   - yudao-module-infra (infrastructure)
echo   - yudao-module-ai (AI features)
echo   - yudao-module-report (reporting)
echo.

cd /d "%PROJECT_ROOT%"
if errorlevel 1 (
    echo [ERROR] Cannot change to project directory
    pause
    exit /b 1
)

echo [STEP 1/3] Cleaning project...
%MAVEN_HOME%\bin\mvn.cmd clean -s "%MAVEN_SETTINGS%" -Dmaven.repo.local="%MAVEN_REPO%" -q
if errorlevel 1 (
    echo [ERROR] Clean failed
    pause
    exit /b 1
)
echo [SUCCESS] Clean completed

echo.
echo [STEP 2/3] Compiling project...
%MAVEN_HOME%\bin\mvn.cmd compile -s "%MAVEN_SETTINGS%" -Dmaven.repo.local="%MAVEN_REPO%" -T 1C
if errorlevel 1 (
    echo [ERROR] Compile failed
    pause
    exit /b 1
)
echo [SUCCESS] Compile completed

echo.
echo [STEP 3/3] Packaging project...
%MAVEN_HOME%\bin\mvn.cmd package -s "%MAVEN_SETTINGS%" -Dmaven.repo.local="%MAVEN_REPO%" -DskipTests -T 1C
if errorlevel 1 (
    echo [ERROR] Package failed
    pause
    exit /b 1
)
echo [SUCCESS] Package completed

echo.
echo ========================================
echo [BUILD SUCCESS] All modules built successfully!
echo ========================================

:: Check JAR file
set JAR_FILE=%PROJECT_ROOT%\yudao-server\target\yudao-server.jar
if exist "%JAR_FILE%" (
    echo [INFO] JAR file created: %JAR_FILE%
    for %%I in ("%JAR_FILE%") do echo [INFO] Size: %%~zI bytes
    echo [INFO] Ready to start with start-final.bat
) else (
    echo [WARNING] JAR file not found at expected location
    echo [INFO] Checking target directory...
    dir "%PROJECT_ROOT%\yudao-server\target\*.jar" 2>nul
)

echo.
echo [INFO] Build completed successfully!
echo [INFO] Next steps:
echo   1. Use start-final.bat to start the application
echo   2. Or use run-final.bat for one-click build and start
echo.
pause
