@echo off
title RuoYi Vue Pro - Import AI Tables

echo ========================================
echo    RuoYi Vue Pro AI Tables Import
echo ========================================
echo.

:: Configuration
set PROJECT_ROOT=G:\devAI\ruoyi-vue-pro-1735
set AI_SQL_FILE=%PROJECT_ROOT%\sql\mysql\ai-init.sql

:: Database configuration (modify these according to your setup)
set DB_HOST=localhost
set DB_PORT=3306
set DB_NAME=ruoyi-vue-pro
set DB_USER=root
set DB_PASSWORD=123456

echo [INFO] AI Module Database Import Script
echo.
echo [INFO] Configuration:
echo   Database Host: %DB_HOST%:%DB_PORT%
echo   Database Name: %DB_NAME%
echo   Database User: %DB_USER%
echo   SQL Script: %AI_SQL_FILE%
echo.

:: Check if AI SQL file exists
if not exist "%AI_SQL_FILE%" (
    echo [ERROR] AI SQL file not found: %AI_SQL_FILE%
    echo [INFO] Please ensure the sql/mysql/ai-init.sql file exists
    pause
    exit /b 1
)

echo [INFO] Found AI SQL script: %AI_SQL_FILE%
echo.

:: Check if MySQL client is available
mysql --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] MySQL client not found in PATH
    echo [INFO] Please install MySQL client or add it to PATH
    echo [INFO] Alternative: Use phpMyAdmin, Navicat, or other database tools
    echo [INFO] to manually import the SQL file: %AI_SQL_FILE%
    pause
    exit /b 1
)

echo [SUCCESS] MySQL client found
echo.

echo [WARNING] This will create AI module tables in database: %DB_NAME%
echo [WARNING] Make sure you have backed up your database if needed
echo.
set /p confirm="Continue with AI tables import? (Y/N): "
if /i not "%confirm%"=="Y" (
    echo [INFO] Import cancelled by user
    pause
    exit /b 0
)

echo.
echo [INFO] Starting AI tables import...
echo [INFO] Importing SQL script: %AI_SQL_FILE%
echo.

:: Import AI tables
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% < "%AI_SQL_FILE%"

if errorlevel 1 (
    echo.
    echo [ERROR] Failed to import AI tables
    echo [INFO] Please check:
    echo   1. Database connection parameters
    echo   2. Database user permissions
    echo   3. Database exists and is accessible
    echo   4. SQL script syntax
    pause
    exit /b 1
)

echo.
echo ========================================
echo [SUCCESS] AI Tables Import Completed!
echo ========================================
echo.

echo [INFO] The following AI tables have been created:
echo   - ai_api_key (AI API keys)
echo   - ai_model (AI models)
echo   - ai_chat_conversation (Chat conversations)
echo   - ai_chat_message (Chat messages)
echo   - ai_knowledge (Knowledge base)
echo   - ai_knowledge_document (Knowledge documents)
echo   - ai_knowledge_segment (Knowledge segments)
echo   - ai_image (AI image generation)
echo   - ai_music (AI music generation)
echo   - ai_mind_map (AI mind maps)
echo.

echo [INFO] Next steps:
echo   1. Restart your RuoYi Vue Pro application
echo   2. Configure AI API keys in the admin panel
echo   3. Set up AI models and platforms
echo   4. Test AI functionality
echo.

echo [INFO] For AI configuration guide, visit:
echo   https://cloud.iocoder.cn/ai/build/
echo.
pause
